{"rustc": 16531013993302481708, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 12841763714740291134, "deps": [[3060637413840920116, "proc_macro2", false, 1615449880013655637], [7341521034400937459, "tauri_codegen", false, 3616594823625595536], [11050281405049894993, "tauri_utils", false, 16122735134604026904], [13077543566650298139, "heck", false, 13003241475100688398], [17990358020177143287, "quote", false, 15017761040731537776], [18149961000318489080, "syn", false, 2644455251149143107]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-39acba1fef98de54\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}