cargo:rerun-if-changed=build.rs
cargo:rerun-if-env-changed=LZMA_API_STATIC
cargo:root=C:\WorkSpace\claudia\src-tauri\target\release\build\lzma-sys-394568b3ffa32667\out
cargo:include=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\lzma-sys-0.1.20\xz-5.2/src/liblzma/api
OUT_DIR = Some(C:\WorkSpace\claudia\src-tauri\target\release\build\lzma-sys-394568b3ffa32667\out)
OPT_LEVEL = Some(3)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(C:\WorkSpace\claudia\src-tauri\target\release\deps;C:\WorkSpace\claudia\src-tauri\target\release;C:\Users\<USER>\.rustup\toolchains\nightly-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\WorkSpace\claudia\node_modules\.bin;C:\WorkSpace\claudia\node_modules\.bin;C:\WorkSpace\node_modules\.bin;C:\node_modules\.bin;C:\Program Files (x86)\Embarcadero\Studio\23.0\bin;C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Bpl;C:\Program Files (x86)\Embarcadero\Studio\23.0\bin64;C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Bpl\Win64;C:\Python313\Scripts\;C:\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\Embarcadero\Studio\22.0\bin;C:\Users\<USER>\Documents\Embarcadero\Studio\22.0\Bpl;C:\Program Files (x86)\Embarcadero\Studio\22.0\bin64;C:\Users\<USER>\Documents\Embarcadero\Studio\22.0\Bpl\Win64;C:\Python311-32\Scripts\;C:\Python311-32\;C:\Python311-64\Scripts\;C:\Python311-64\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Bandizip\;C:\mingw64\bin;C:\Users\<USER>\.cargo\bin;C:\anaconda3;C:\anaconda3\Library\mingw-w64\bin;C:\anaconda3\Library\usr\bin;C:\anaconda3\Library\bin;C:\anaconda3\Scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\ffmpeg\bin;C:\Program Files\Zero Install;C:\MinGW\bin;C:\MinGW\msys\1.0\bin;C:\Strawberry\c\bin;C:\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\cursor\resources\app\bin;C:\Program Files\Go\bin;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\scoop\shims;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Qt\5.14.0\msvc2017_64\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code Insiders\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\SysWow64\;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\.rustup\toolchains\nightly-x86_64-pc-windows-msvc\bin)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,lahfsahf,sse,sse2,sse3,x87)
DEBUG = Some(false)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
tuklib_cpucores.c
tuklib_physmem.c
xz-5.2/src/common/tuklib_physmem.c(82): warning C4996: 'GetVersion': deprecated로 선언되었습니다.
check.c
crc32_fast.c
crc32_table.c
crc64_fast.c
xz-5.2/src/liblzma/check\crc64_fast.c(52): warning C4244: '초기화 중': 'uint64_t'에서 'const uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
crc64_table.c
sha256.c
alone_decoder.c
xz-5.2/src/liblzma/lz\lz_decoder.h(132): warning C4267: '초기화 중': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_decoder.h(159): warning C4267: '초기화 중': 'size_t'에서 'const uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_decoder.h(160): warning C4267: '초기화 중': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
alone_encoder.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
auto_decoder.c
block_buffer_decoder.c
block_buffer_encoder.c
xz-5.2/src/liblzma/common\block_buffer_encoder.c(147): warning C4267: '=': 'size_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
block_decoder.c
block_encoder.c
xz-5.2/src/liblzma/common\block_encoder.c(150): warning C4100: 'filters': 참조되지 않은 정식 매개 변수입니다.
block_header_decoder.c
block_header_encoder.c
xz-5.2/src/liblzma/common\block_header_encoder.c(85): warning C4267: '=': 'size_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
block_util.c
common.c
easy_buffer_encoder.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
easy_decoder_memusage.c
easy_encoder.c
easy_encoder_memusage.c
easy_preset.c
filter_buffer_decoder.c
filter_buffer_encoder.c
filter_common.c
filter_decoder.c
filter_encoder.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
filter_flags_decoder.c
filter_flags_encoder.c
hardware_cputhreads.c
hardware_physmem.c
index.c
xz-5.2/src/liblzma/common\index.c(102): warning C4200: 비표준 확장이 사용됨: 구조체/공용 구조체의 배열 크기가 0입니다.
index_decoder.c
xz-5.2/src/liblzma/common\index_decoder.c(63): warning C4100: 'action': 참조되지 않은 정식 매개 변수입니다.
xz-5.2/src/liblzma/common\index_decoder.c(62): warning C4100: 'out_size': 참조되지 않은 정식 매개 변수입니다.
xz-5.2/src/liblzma/common\index_decoder.c(61): warning C4100: 'out_pos': 참조되지 않은 정식 매개 변수입니다.
xz-5.2/src/liblzma/common\index_decoder.c(60): warning C4100: 'out': 참조되지 않은 정식 매개 변수입니다.
index_encoder.c
xz-5.2/src/liblzma/common\index_encoder.c(51): warning C4100: 'action': 참조되지 않은 정식 매개 변수입니다.
xz-5.2/src/liblzma/common\index_encoder.c(48): warning C4100: 'in_size': 참조되지 않은 정식 매개 변수입니다.
xz-5.2/src/liblzma/common\index_encoder.c(47): warning C4100: 'in_pos': 참조되지 않은 정식 매개 변수입니다.
xz-5.2/src/liblzma/common\index_encoder.c(46): warning C4100: 'in': 참조되지 않은 정식 매개 변수입니다.
xz-5.2/src/liblzma/common\index_encoder.c(45): warning C4100: 'allocator': 참조되지 않은 정식 매개 변수입니다.
index_hash.c
outqueue.c
stream_buffer_decoder.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
stream_buffer_encoder.c
stream_decoder.c
stream_encoder.c
stream_encoder_mt.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\lzma-sys-0.1.20\xz-5.2\src\liblzma\common\stream_encoder_mt.c(607) : warning C4701: 초기화되지 않았을 수 있는 'ret' 지역 변수를 사용했습니다.
stream_flags_common.c
stream_flags_decoder.c
stream_flags_encoder.c
vli_decoder.c
vli_encoder.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
vli_size.c
delta_common.c
delta_decoder.c
delta_encoder.c
xz-5.2/src/liblzma/delta\delta_encoder.c(90): warning C4100: 'filters_null': 참조되지 않은 정식 매개 변수입니다.
xz-5.2/src/liblzma/delta\delta_encoder.c(122): warning C4244: '=': 'const uint32_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
lz_decoder.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\lzma-sys-0.1.20\xz-5.2\src\liblzma\lz\lz_decoder.h(132): warning C4267: '초기화 중': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\lzma-sys-0.1.20\xz-5.2\src\liblzma\lz\lz_decoder.h(159): warning C4267: '초기화 중': 'size_t'에서 'const uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\lzma-sys-0.1.20\xz-5.2\src\liblzma\lz\lz_decoder.h(160): warning C4267: '초기화 중': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
lz_encoder.c
xz-5.2/src/liblzma/lz\lz_encoder.c(111): warning C4267: '=': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_encoder.c(205): warning C4267: '=': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_encoder.c(208): warning C4267: '=': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_encoder.c(226): warning C4267: '+=': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_encoder.c(239): warning C4267: '=': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_encoder.c(240): warning C4267: '=': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_encoder.c(257): warning C4267: '=': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_encoder.c(310): warning C4267: '=': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_encoder.c(221): warning C4267: '초기화 중': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_encoder.c(508): warning C4100: 'filters_null': 참조되지 않은 정식 매개 변수입니다.
lz_encoder_mf.c
xz-5.2/src/liblzma/lz\lz_encoder_mf.c(333): warning C4244: '=': '__int64'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_encoder_mf.c(412): warning C4244: '=': '__int64'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_encoder_mf.c(596): warning C4244: '=': '__int64'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_encoder_mf.c(648): warning C4244: '=': '__int64'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_encoder_mf.c(721): warning C4244: '=': '__int64'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
fastpos_table.c
lzma2_decoder.c
lzma2_encoder.c
lzma_decoder.c
lzma_encoder.c
xz-5.2/src/liblzma/lz\lz_decoder.h(132): warning C4267: '초기화 중': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_decoder.h(159): warning C4267: '초기화 중': 'size_t'에서 'const uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_decoder.h(160): warning C4267: '초기화 중': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
lzma_encoder_optimum_fast.c
xz-5.2/src/liblzma/lzma\lzma2_encoder.c(85): warning C4267: '+=': 'size_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lzma\lzma2_encoder.c(91): warning C4267: '=': 'size_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lzma\lzma2_encoder.c(126): warning C4267: '=': 'size_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lzma\lzma2_encoder.c(170): warning C4267: '초기화 중': 'size_t'에서 'const uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lzma\lzma2_encoder.c(397): warning C4244: '=': 'uint32_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
lzma_encoder_optimum_normal.c
xz-5.2/src/liblzma/lz\lz_decoder.h(132): warning C4267: '초기화 중': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_decoder.h(159): warning C4267: '초기화 중': 'size_t'에서 'const uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lz\lz_decoder.h(160): warning C4267: '초기화 중': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/lzma\lzma_decoder.c(485): warning C4244: '함수': 'uint32_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
lzma_encoder_presets.c
xz-5.2/src/liblzma/lzma\lzma_decoder.c(1007): warning C4244: '-=': 'uint32_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
price_table.c
xz-5.2/src/liblzma/lzma\lzma_encoder.c(650): warning C4244: '=': 'const uint32_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
arm.c
armthumb.c
ia64.c
powerpc.c
simple_coder.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\lzma-sys-0.1.20\xz-5.2\src\liblzma\simple\simple_private.h(62): warning C4200: 비표준 확장이 사용됨: 구조체/공용 구조체의 배열 크기가 0입니다.
xz-5.2/src/liblzma/simple\arm.c(37): warning C4244: '=': 'uint32_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/simple\arm.c(38): warning C4244: '=': 'uint32_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/simple\arm.c(39): warning C4244: '=': 'uint32_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/simple\arm.c(18): warning C4100: 'simple': 참조되지 않은 정식 매개 변수입니다.
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\lzma-sys-0.1.20\xz-5.2\src\liblzma\simple\simple_private.h(62): warning C4200: 비표준 확장이 사용됨: 구조체/공용 구조체의 배열 크기가 0입니다.
xz-5.2/src/liblzma/simple\armthumb.c(41): warning C4244: '=': 'uint32_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/simple\armthumb.c(43): warning C4244: '=': 'uint32_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/simple\armthumb.c(18): warning C4100: 'simple': 참조되지 않은 정식 매개 변수입니다.
simple_decoder.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\lzma-sys-0.1.20\xz-5.2\src\liblzma\simple\simple_private.h(62): warning C4200: 비표준 확장이 사용됨: 구조체/공용 구조체의 배열 크기가 0입니다.
xz-5.2/src/liblzma/simple\ia64.c(18): warning C4100: 'simple': 참조되지 않은 정식 매개 변수입니다.
simple_encoder.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\lzma-sys-0.1.20\xz-5.2\src\liblzma\simple\simple_private.h(62): warning C4200: 비표준 확장이 사용됨: 구조체/공용 구조체의 배열 크기가 0입니다.
xz-5.2/src/liblzma/simple\powerpc.c(41): warning C4244: '=': 'uint32_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/simple\powerpc.c(42): warning C4244: '=': 'uint32_t'에서 'uint8_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/simple\powerpc.c(18): warning C4100: 'simple': 참조되지 않은 정식 매개 변수입니다.
sparc.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\lzma-sys-0.1.20\xz-5.2\src\liblzma\simple\simple_private.h(62): warning C4200: 비표준 확장이 사용됨: 구조체/공용 구조체의 배열 크기가 0입니다.
xz-5.2/src/liblzma/simple\simple_coder.c(63): warning C4267: '+=': 'size_t'에서 'uint32_t'(으)로 변환하면서 데이터가 손실될 수 있습니다.
xz-5.2/src/liblzma/simple\simple_coder.c(223): warning C4100: 'filters_null': 참조되지 않은 정식 매개 변수입니다.
x86.c
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\lzma-sys-0.1.20\xz-5.2\src\liblzma\simple\simple_private.h(62): warning C4200: 비표준 확장이 사용됨: 구조체/공용 구조체의 배열 크기가 0입니다.
xz-5.2/src/liblzma/simple\sparc.c(18): warning C4100: 'simple': 참조되지 않은 정식 매개 변수입니다.
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\lzma-sys-0.1.20\xz-5.2\src\liblzma\simple\simple_private.h(62): warning C4200: 비표준 확장이 사용됨: 구조체/공용 구조체의 배열 크기가 0입니다.
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-lib=static=lzma
cargo:rustc-link-search=native=C:\WorkSpace\claudia\src-tauri\target\release\build\lzma-sys-394568b3ffa32667\out
