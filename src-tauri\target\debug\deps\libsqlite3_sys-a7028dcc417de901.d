C:\WorkSpace\claudia\src-tauri\target\debug\deps\libsqlite3_sys-a7028dcc417de901.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.34.0\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.34.0\src\error.rs C:\WorkSpace\claudia\src-tauri\target\debug\build\libsqlite3-sys-d62757b1f4e8c642\out/bindgen.rs

C:\WorkSpace\claudia\src-tauri\target\debug\deps\liblibsqlite3_sys-a7028dcc417de901.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.34.0\src\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.34.0\src\error.rs C:\WorkSpace\claudia\src-tauri\target\debug\build\libsqlite3-sys-d62757b1f4e8c642\out/bindgen.rs

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.34.0\src\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.34.0\src\error.rs:
C:\WorkSpace\claudia\src-tauri\target\debug\build\libsqlite3-sys-d62757b1f4e8c642\out/bindgen.rs:

# env-dep:OUT_DIR=C:\\WorkSpace\\claudia\\src-tauri\\target\\debug\\build\\libsqlite3-sys-d62757b1f4e8c642\\out
