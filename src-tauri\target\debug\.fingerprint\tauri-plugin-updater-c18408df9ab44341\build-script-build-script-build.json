{"rustc": 16531013993302481708, "features": "[\"default\", \"rustls-tls\", \"zip\"]", "declared_features": "[\"default\", \"native-tls\", \"native-tls-vendored\", \"rustls-tls\", \"zip\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 2427601551842899375, "deps": [[2326493920556799156, "tauri_plugin", false, 15291489629692764754]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-updater-c18408df9ab44341\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}