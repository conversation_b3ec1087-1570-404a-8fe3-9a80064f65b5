{"rustc": 16531013993302481708, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 15871453406282790652, "deps": [[561782849581144631, "html5ever", false, 12526675876891066955], [1200537532907108615, "url<PERSON><PERSON>n", false, 218710138485690280], [3060637413840920116, "proc_macro2", false, 1615449880013655637], [3129130049864710036, "memchr", false, 11761170490549452041], [3150220818285335163, "url", false, 6102992118273930879], [3191507132440681679, "serde_untagged", false, 2056867597957533059], [4899080583175475170, "semver", false, 6284967286266319008], [5986029879202738730, "log", false, 16717307215505850697], [6213549728662707793, "serde_with", false, 1767775556553075998], [6262254372177975231, "kuchiki", false, 39218546944135057], [6606131838865521726, "ctor", false, 1336507788692984183], [6913375703034175521, "schemars", false, 5851682215235040678], [7170110829644101142, "json_patch", false, 279542947189289318], [8319709847752024821, "uuid", false, 17576429577715121075], [9010263965687315507, "http", false, 12229602069060356524], [9451456094439810778, "regex", false, 9543725131571474675], [9689903380558560274, "serde", false, 17697301536407558215], [10806645703491011684, "thiserror", false, 12848503974094196907], [11655476559277113544, "cargo_metadata", false, 7594755987301722484], [11989259058781683633, "dunce", false, 9398898499214381831], [13625485746686963219, "anyhow", false, 5661029371983558191], [14132538657330703225, "brotli", false, 9869235566277786718], [15367738274754116744, "serde_json", false, 10606268451690261834], [15609422047640926750, "toml", false, 8888550214211600567], [15622660310229662834, "walkdir", false, 7957375469078984964], [17146114186171651583, "infer", false, 7558653216561099221], [17155886227862585100, "glob", false, 16471218984395200217], [17186037756130803222, "phf", false, 8543980290581845122], [17990358020177143287, "quote", false, 15017761040731537776]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-62090fddbc00affe\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}