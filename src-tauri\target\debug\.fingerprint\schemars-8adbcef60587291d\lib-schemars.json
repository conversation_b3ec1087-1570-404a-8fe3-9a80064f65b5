{"rustc": 16531013993302481708, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 3881519987043930164, "deps": [[3150220818285335163, "url", false, 6102992118273930879], [6913375703034175521, "build_script_build", false, 14313773298566475695], [8319709847752024821, "uuid1", false, 17576429577715121075], [9122563107207267705, "dyn_clone", false, 4892010922611844691], [9689903380558560274, "serde", false, 17697301536407558215], [14923790796823607459, "indexmap", false, 5345949646378880647], [15367738274754116744, "serde_json", false, 10606268451690261834], [16071897500792579091, "schemars_derive", false, 144114077535497913]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-8adbcef60587291d\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}