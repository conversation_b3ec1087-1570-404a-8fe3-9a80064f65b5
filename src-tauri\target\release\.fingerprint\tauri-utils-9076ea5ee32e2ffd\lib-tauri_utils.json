{"rustc": 16531013993302481708, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 15871453406282790652, "deps": [[561782849581144631, "html5ever", false, 14647044195030320712], [1200537532907108615, "url<PERSON><PERSON>n", false, 6718250427926080122], [3129130049864710036, "memchr", false, 10117494724452780945], [3150220818285335163, "url", false, 9955794519733322107], [3191507132440681679, "serde_untagged", false, 13019979838461725005], [4899080583175475170, "semver", false, 12278267656750287236], [5986029879202738730, "log", false, 15145306266573603605], [6213549728662707793, "serde_with", false, 11357132003997931578], [6262254372177975231, "kuchiki", false, 15369725967029580736], [6606131838865521726, "ctor", false, 5436888814629660613], [7170110829644101142, "json_patch", false, 17861390520497262630], [8319709847752024821, "uuid", false, 114743279593149165], [9010263965687315507, "http", false, 11117590330939092863], [9451456094439810778, "regex", false, 18184761356493868869], [9689903380558560274, "serde", false, 7565272720106024856], [10806645703491011684, "thiserror", false, 3262636517690463515], [11989259058781683633, "dunce", false, 11998082795202031044], [13625485746686963219, "anyhow", false, 18019008218539212579], [14132538657330703225, "brotli", false, 2461126800861299959], [15367738274754116744, "serde_json", false, 8303730601567159926], [15609422047640926750, "toml", false, 1206043067734506723], [15622660310229662834, "walkdir", false, 1197033589297995617], [17146114186171651583, "infer", false, 4148077889560015891], [17155886227862585100, "glob", false, 938717186080104040], [17186037756130803222, "phf", false, 16631980132763516064]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-9076ea5ee32e2ffd\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}