{"rustc": 16531013993302481708, "features": "[\"default\", \"gzip\", \"socks-proxy\", \"tls\"]", "declared_features": "[\"brotli\", \"charset\", \"cookies\", \"default\", \"gzip\", \"http-crate\", \"http-interop\", \"json\", \"native-certs\", \"native-tls\", \"proxy-from-env\", \"socks-proxy\", \"testdeps\", \"tls\"]", "target": 2636997325719059094, "profile": 1369601567987815722, "path": 16420331238004747001, "deps": [[1129418674938548854, "socks", false, 1671791950345779908], [2883436298747778685, "rustls_pki_types", false, 10344219780024642299], [3150220818285335163, "url", false, 2866603802530543272], [3722963349756955755, "once_cell", false, 5806442715378666544], [5986029879202738730, "log", false, 9966600394995078573], [8156804143951879168, "webpki_roots", false, 16024859949527602888], [13077212702700853852, "base64", false, 17713193311424426127], [16400140949089969347, "rustls", false, 12874669373141878833], [17772299992546037086, "flate2", false, 7399051406409634280]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\ureq-f9c2da844d1ca341\\dep-lib-ureq", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}