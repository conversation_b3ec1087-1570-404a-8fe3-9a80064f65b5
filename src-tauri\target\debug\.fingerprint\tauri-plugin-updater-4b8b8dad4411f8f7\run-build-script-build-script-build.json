{"rustc": 16531013993302481708, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 12218645810558505789], [18440762029541581206, "build_script_build", false, 11480141721464742468]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-updater-4b8b8dad4411f8f7\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}