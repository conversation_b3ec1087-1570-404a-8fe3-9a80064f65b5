{"rustc": 16531013993302481708, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9805105373369294601, "build_script_build", false, 2064088662001629456], [10755362358622467486, "build_script_build", false, 12218645810558505789], [13919194856117907555, "build_script_build", false, 11369014245380116423], [3834743577069889284, "build_script_build", false, 13620751302696792674], [13890802266741835355, "build_script_build", false, 8033386978985984147], [246920333930397414, "build_script_build", false, 88128781726740396], [15441187897486245138, "build_script_build", false, 10109897015673730057], [7849236192756901113, "build_script_build", false, 13100272225610427585], [17962022290347926134, "build_script_build", false, 17856410001937789656], [1582828171158827377, "build_script_build", false, 4379765858623735978], [18440762029541581206, "build_script_build", false, 1061395154741265433]], "local": [{"RerunIfChanged": {"output": "debug\\build\\claudia-c4b52349506f9cda\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}