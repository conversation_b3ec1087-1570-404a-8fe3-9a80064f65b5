{"rustc": 16531013993302481708, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 7795770796983219439, "profile": 15657897354478470176, "path": 15281685881539096402, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 8099047492367206136], [3150220818285335163, "url", false, 8382040255407682105], [8218178811151724123, "reqwest", false, 2297656379068550951], [8298091525883606470, "cookie_store", false, 5651138308444040432], [9010263965687315507, "http", false, 12229602069060356524], [9451456094439810778, "regex", false, 9543725131571474675], [9538054652646069845, "tokio", false, 17887723141469624085], [9689903380558560274, "serde", false, 498733323001508635], [10755362358622467486, "tauri", false, 14358646239143990346], [10806645703491011684, "thiserror", false, 12848503974094196907], [13890802266741835355, "tauri_plugin_fs", false, 1487360620877737583], [15367738274754116744, "serde_json", false, 15963293933936563447], [15441187897486245138, "build_script_build", false, 10109897015673730057], [16066129441945555748, "bytes", false, 1700372560425586026], [17047088963840213854, "data_url", false, 969491418025857133]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-http-97c9be236607d42d\\dep-lib-tauri_plugin_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}