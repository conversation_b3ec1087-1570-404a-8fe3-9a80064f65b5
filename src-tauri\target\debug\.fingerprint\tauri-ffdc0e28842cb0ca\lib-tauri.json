{"rustc": 16531013993302481708, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"http-range\", \"image\", \"image-png\", \"protocol-asset\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 9185822350802405798, "deps": [[40386456601120721, "percent_encoding", false, 17727371605150502173], [442785307232013896, "tauri_runtime", false, 9070606209579826333], [1200537532907108615, "url<PERSON><PERSON>n", false, 8002641282035014302], [3150220818285335163, "url", false, 12307826154636159061], [4143744114649553716, "raw_window_handle", false, 943706900783983373], [4341921533227644514, "muda", false, 2112966079722576583], [4919829919303820331, "serialize_to_javascript", false, 14197796435770806740], [5986029879202738730, "log", false, 3108541596750279158], [7752760652095876438, "tauri_runtime_wry", false, 17310077880033425890], [8351317599104215083, "tray_icon", false, 7906182538561934981], [8539587424388551196, "webview2_com", false, 14896280931384664215], [8866577183823226611, "http_range", false, 8620688901876497161], [9010263965687315507, "http", false, 5374916934768255852], [9228235415475680086, "tauri_macros", false, 1051913553662461954], [9538054652646069845, "tokio", false, 11647163168096858975], [9689903380558560274, "serde", false, 1356923275834828457], [9920160576179037441, "getrandom", false, 12501570872951967353], [10229185211513642314, "mime", false, 6609920080389387013], [10629569228670356391, "futures_util", false, 6897336821567239983], [10755362358622467486, "build_script_build", false, 16468743714951719541], [10806645703491011684, "thiserror", false, 8844752790588992264], [11050281405049894993, "tauri_utils", false, 9186580297950028098], [11989259058781683633, "dunce", false, 12799886613012291701], [12565293087094287914, "window_vibrancy", false, 9321560115956882779], [12986574360607194341, "serde_repr", false, 18191837527263456998], [13028763805764736075, "image", false, 13536220855850373670], [13077543566650298139, "heck", false, 12072215744299642774], [13116089016666501665, "windows", false, 3882630998416848268], [13625485746686963219, "anyhow", false, 14958058165012313471], [15367738274754116744, "serde_json", false, 5032607260025433330], [16928111194414003569, "dirs", false, 10291156319872330776], [17155886227862585100, "glob", false, 17585169469182333978]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-ffdc0e28842cb0ca\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}