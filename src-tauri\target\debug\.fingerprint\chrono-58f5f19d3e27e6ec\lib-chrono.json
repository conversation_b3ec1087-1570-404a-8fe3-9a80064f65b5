{"rustc": 16531013993302481708, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 15315924755136109342, "profile": 15657897354478470176, "path": 6176114412090854716, "deps": [[1885655767270534569, "windows_link", false, 2100394237888583491], [5157631553186200874, "num_traits", false, 13119249483829798859], [9689903380558560274, "serde", false, 498733323001508635]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\chrono-58f5f19d3e27e6ec\\dep-lib-chrono", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}