{"rustc": 16531013993302481708, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 12677452500306300857, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 218710138485690280], [2326493920556799156, "tauri_plugin", false, 15291489629692764754], [3150220818285335163, "url", false, 6102992118273930879], [6913375703034175521, "schemars", false, 5851682215235040678], [9451456094439810778, "regex", false, 9543725131571474675], [9689903380558560274, "serde", false, 17697301536407558215]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-http-9ba459a430e76370\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}