{"rustc": 16531013993302481708, "features": "[]", "declared_features": "[\"win7-notifications\", \"windows-version\", \"windows7-compat\"]", "target": 11906320761866078153, "profile": 2040997289075261528, "path": 13657914234891729876, "deps": [[947818755262499932, "notify_rust", false, 1725217695193844637], [3150220818285335163, "url", false, 9955794519733322107], [5986029879202738730, "log", false, 15145306266573603605], [7849236192756901113, "build_script_build", false, 13539627951794035001], [9689903380558560274, "serde", false, 7565272720106024856], [10755362358622467486, "tauri", false, 17824018193451593779], [10806645703491011684, "thiserror", false, 3262636517690463515], [12409575957772518135, "time", false, 18134323767103071558], [12986574360607194341, "serde_repr", false, 15864135832470065171], [13208667028893622512, "rand", false, 18076104853142829034], [15367738274754116744, "serde_json", false, 8303730601567159926]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-notification-3d481095b5548b06\\dep-lib-tauri_plugin_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}