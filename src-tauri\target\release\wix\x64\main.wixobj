﻿<?xml version="1.0" encoding="utf-8"?><wixObject version="3.0.2002.0" xmlns="http://schemas.microsoft.com/wix/2006/objects"><section id="*" type="product"><table name="_SummaryInformation"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*23"><field>1</field><field>!(loc.TauriCodepage)</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*23"><field>2</field><field>Installation Database</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*23"><field>3</field><field>Claudia</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*23"><field>4</field><field>asterisk</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*23"><field>5</field><field>Installer</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*23"><field>6</field><field>This installer database contains the logic and data required to install Claudia.</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*23"><field>7</field><field>x64;0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*23"><field>9</field><field>*</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*23"><field>14</field><field>450</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*23"><field>15</field><field>2</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*23"><field>19</field><field>2</field></row></table><table name="AppSearch"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*55"><field>INSTALLDIR</field><field>PrevInstallDirWithName</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*55"><field>INSTALLDIR</field><field>PrevInstallDirNoName</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*187"><field>WVRTINSTALLED</field><field>WVRTInstalledSystem</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*187"><field>WVRTINSTALLED</field><field>WVRTInstalledUser</field></row></table><table name="Component"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*91"><field>ApplicationShortcutDesktop</field><field>*</field><field>DesktopFolder</field><field>260</field><field /><field>reg5EE5A25C333804C39299E7C7A1BBAA39</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*106"><field>RegistryEntries</field><field>*</field><field>INSTALLDIR</field><field>260</field><field /><field>reg52E2B6CA8A7D1C3CAE3DE32607D96BDD</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*112"><field>Path</field><field>{7C65763D-54FE-5138-943C-229A00A7925B}</field><field>INSTALLDIR</field><field>256</field><field /><field>Path</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*116"><field>CMP_UninstallShortcut</field><field>*</field><field>INSTALLDIR</field><field>260</field><field /><field>reg9AC40DD30627B3CC408745773CA214D9</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*137"><field>ApplicationShortcut</field><field>*</field><field>ApplicationProgramsFolder</field><field>260</field><field /><field>regDA840F504A21CB30723CFC538E9EE428</field></row></table><table name="ControlEvent"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*70"><field>ExitDialog</field><field>Finish</field><field>DoAction</field><field>LaunchApplication</field><field>WIXUI_EXITDIALOGOPTIONALCHECKBOX = 1 and NOT Installed</field><field>1</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*75"><field>WelcomeDlg</field><field>Next</field><field>NewDialog</field><field>InstallDirDlg</field><field>1</field><field>2</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*80"><field>InstallDirDlg</field><field>Back</field><field>NewDialog</field><field>WelcomeDlg</field><field>1</field><field>2</field></row></table><table name="CustomAction"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*52"><field>SetARPNOMODIFY</field><field>51</field><field>ARPNOMODIFY</field><field>1</field><field /></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*66"><field>LaunchApplication</field><field>210</field><field>Path</field><field>[LAUNCHAPPARGS]</field><field /></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*192"><field>DownloadAndInvokeBootstrapper</field><field>1058</field><field>INSTALLDIR</field><field>powershell.exe -NoProfile -windowstyle hidden try [\{] [\[]Net.ServicePointManager[\]]::SecurityProtocol = [\[]Net.SecurityProtocolType[\]]::Tls12 [\}] catch [\{][\}]; Invoke-WebRequest -Uri "https://go.microsoft.com/fwlink/p/?LinkId=2124703" -OutFile "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" ; Start-Process -FilePath "$env:TEMP\MicrosoftEdgeWebview2Setup.exe" -ArgumentList ('/silent', '/install') -Wait</field><field /></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*209"><field>SetARPINSTALLLOCATION</field><field>51</field><field>ARPINSTALLLOCATION</field><field>[INSTALLDIR]</field><field /></row></table><table name="Directory"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*90"><field>DesktopFolder</field><field>TARGETDIR</field><field>Desktop</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*98"><field>INSTALLDIR</field><field>ProgramFiles64Folder</field><field>Claudia</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*97"><field>ProgramFiles64Folder</field><field>TARGETDIR</field><field>PFiles</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*101"><field>ApplicationProgramsFolder</field><field>ProgramMenuFolder</field><field>Claudia</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*100"><field>ProgramMenuFolder</field><field>TARGETDIR</field><field>.</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*89"><field>TARGETDIR</field><field /><field>SourceDir</field></row></table><table name="Feature"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*164"><field>ShortcutsFeature</field><field /><field>Shortcuts</field><field /><field>2</field><field>1</field><field /><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*173"><field>Environment</field><field /><field>PATH Environment Variable</field><field>!(loc.PathEnvVarFeature)</field><field>4</field><field>1</field><field /><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*151"><field>MainProgram</field><field /><field>Application</field><field>!(loc.InstallAppFeature)</field><field>1</field><field>1</field><field>INSTALLDIR</field><field>24</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*183"><field>External</field><field /><field /><field /><field>2</field><field>1</field><field /><field>24</field></row></table><table name="File"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*113"><field>Path</field><field>Path</field><field>claudia.exe</field><field>0</field><field /><field /><field>1536</field><field /></row></table><table name="Icon"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*49"><field>ProductIcon</field><field>C:\WorkSpace\claudia\src-tauri\target\release\resources\icon.ico</field></row></table><table name="Media"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*46"><field>1</field><field>0</field><field /><field>#app.cab</field><field /><field /></row></table><table name="MsiShortcutProperty"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*144"><field>scpA928DBE89B302EBC9C3D7CA666BC5A03</field><field>ApplicationStartMenuShortcut</field><field>System.AppUserModel.ID</field><field>claudia.asterisk.so</field></row></table><table name="Property"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*23"><field>ALLUSERS</field><field>1</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*33"><field>REINSTALLMODE</field><field>amus</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*36"><field>AUTOLAUNCHAPP</field><field /></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*38"><field>LAUNCHAPPARGS</field><field /></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*50"><field>ARPPRODUCTICON</field><field>ProductIcon</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*51"><field>ARPNOREPAIR</field><field>yes</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*55"><field>INSTALLDIR</field><field /></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*64"><field>WIXUI_EXITDIALOGOPTIONALCHECKBOXTEXT</field><field>!(loc.LaunchApp)</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*65"><field>WIXUI_EXITDIALOGOPTIONALCHECKBOX</field><field>1</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*72"><field>WIXUI_INSTALLDIR</field><field>INSTALLDIR</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*187"><field>WVRTINSTALLED</field><field /></row></table><table name="Registry"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*94"><field>reg5EE5A25C333804C39299E7C7A1BBAA39</field><field>1</field><field>Software\asterisk\Claudia</field><field>Desktop Shortcut</field><field>#1</field><field>ApplicationShortcutDesktop</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*108"><field>reg52E2B6CA8A7D1C3CAE3DE32607D96BDD</field><field>1</field><field>Software\asterisk\Claudia</field><field>InstallDir</field><field>[INSTALLDIR]</field><field>RegistryEntries</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*127"><field>reg9AC40DD30627B3CC408745773CA214D9</field><field>1</field><field>Software\asterisk\Claudia</field><field>Uninstaller Shortcut</field><field>#1</field><field>CMP_UninstallShortcut</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*147"><field>regDA840F504A21CB30723CFC538E9EE428</field><field>1</field><field>Software\asterisk\Claudia</field><field>Start Menu Shortcut</field><field>#1</field><field>ApplicationShortcut</field></row></table><table name="RegLocator"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*57"><field>PrevInstallDirWithName</field><field>1</field><field>Software\asterisk\Claudia</field><field>InstallDir</field><field>18</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*60"><field>PrevInstallDirNoName</field><field>1</field><field>Software\asterisk\Claudia</field><field /><field>18</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*188"><field>WVRTInstalledSystem</field><field>2</field><field>SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}</field><field>pv</field><field>2</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*189"><field>WVRTInstalledUser</field><field>1</field><field>SOFTWARE\Microsoft\EdgeUpdate\Clients\{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}</field><field>pv</field><field>18</field></row></table><table name="RemoveFile"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*93"><field>DesktopFolder</field><field>ApplicationShortcutDesktop</field><field /><field>DesktopFolder</field><field>2</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*124"><field>INSTALLDIR</field><field>CMP_UninstallShortcut</field><field /><field>INSTALLDIR</field><field>2</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*146"><field>ApplicationProgramsFolder</field><field>ApplicationShortcut</field><field /><field>ApplicationProgramsFolder</field><field>2</field></row></table><table name="Shortcut"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*92"><field>ApplicationDesktopShortcut</field><field>DesktopFolder</field><field>Claudia</field><field>ApplicationShortcutDesktop</field><field>[!Path]</field><field /><field>Runs Claudia</field><field /><field /><field /><field /><field>INSTALLDIR</field><field /><field /><field /><field /></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*118"><field>UninstallShortcut</field><field>INSTALLDIR</field><field>stdhd8vc|Uninstall Claudia</field><field>CMP_UninstallShortcut</field><field>[System64Folder]msiexec.exe</field><field>/x [ProductCode]</field><field>Uninstalls Claudia</field><field /><field /><field /><field /><field /><field /><field /><field /><field /></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*138"><field>ApplicationStartMenuShortcut</field><field>ApplicationProgramsFolder</field><field>Claudia</field><field>ApplicationShortcut</field><field>[!Path]</field><field /><field>Runs Claudia</field><field /><field>ProductIcon</field><field /><field /><field>INSTALLDIR</field><field /><field /><field /><field /></row></table><table name="Upgrade"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*40"><field>{49D01237-4D4C-5579-99AA-47B9BD03E500}</field><field>0</field><field /><field /><field>257</field><field /><field>WIX_UPGRADE_DETECTED</field></row></table><table name="WixAction"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*40"><field>InstallExecuteSequence</field><field>RemoveExistingProducts</field><field /><field /><field /><field>InstallInitialize</field><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*43"><field>InstallExecuteSequence</field><field>RemoveShortcuts</field><field>Installed AND NOT UPGRADINGPRODUCTCODE</field><field /><field /><field /><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*52"><field>InstallExecuteSequence</field><field>SetARPNOMODIFY</field><field /><field /><field /><field>InstallValidate</field><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*194"><field>InstallExecuteSequence</field><field>DownloadAndInvokeBootstrapper</field><field>NOT(REMOVE OR WVRTINSTALLED)</field><field /><field>InstallFinalize</field><field /><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*206"><field>InstallExecuteSequence</field><field>LaunchApplication</field><field>AUTOLAUNCHAPP AND NOT Installed</field><field /><field /><field>InstallFinalize</field><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*209"><field>InstallUISequence</field><field>SetARPINSTALLLOCATION</field><field /><field /><field /><field>CostFinalize</field><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*209"><field>InstallExecuteSequence</field><field>SetARPINSTALLLOCATION</field><field /><field /><field /><field>CostFinalize</field><field>0</field></row></table><table name="WixComplexReference"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*161"><field>MainProgram</field><field>1</field><field /><field>RegistryEntries</field><field>1</field><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*167"><field>ShortcutsFeature</field><field>1</field><field /><field>Path</field><field>1</field><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*168"><field>ShortcutsFeature</field><field>1</field><field /><field>CMP_UninstallShortcut</field><field>1</field><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*169"><field>ShortcutsFeature</field><field>1</field><field /><field>ApplicationShortcut</field><field>1</field><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*170"><field>ShortcutsFeature</field><field>1</field><field /><field>ApplicationShortcutDesktop</field><field>1</field><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*164"><field>MainProgram</field><field>1</field><field /><field>ShortcutsFeature</field><field>2</field><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*179"><field>Environment</field><field>1</field><field /><field>Path</field><field>1</field><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*173"><field>MainProgram</field><field>1</field><field /><field>Environment</field><field>2</field><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*151"><field>*</field><field>5</field><field /><field>MainProgram</field><field>2</field><field>0</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*183"><field>*</field><field>5</field><field /><field>External</field><field>2</field><field>0</field></row></table><table name="WixFile"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*113"><field>Path</field><field /><field /><field /><field>INSTALLDIR</field><field>1</field><field>C:\WorkSpace\claudia\src-tauri\target\release\claudia.exe</field><field /><field>-1</field><field>0</field><field>0</field><field /><field /><field /><field /></row></table><table name="WixGroup"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*161"><field>MainProgram</field><field>Feature</field><field>RegistryEntries</field><field>Component</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*167"><field>ShortcutsFeature</field><field>Feature</field><field>Path</field><field>Component</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*168"><field>ShortcutsFeature</field><field>Feature</field><field>CMP_UninstallShortcut</field><field>Component</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*169"><field>ShortcutsFeature</field><field>Feature</field><field>ApplicationShortcut</field><field>Component</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*170"><field>ShortcutsFeature</field><field>Feature</field><field>ApplicationShortcutDesktop</field><field>Component</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*164"><field>MainProgram</field><field>Feature</field><field>ShortcutsFeature</field><field>Feature</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*179"><field>Environment</field><field>Feature</field><field>Path</field><field>Component</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*173"><field>MainProgram</field><field>Feature</field><field>Environment</field><field>Feature</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*151"><field>*</field><field>Product</field><field>MainProgram</field><field>Feature</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*183"><field>*</field><field>Product</field><field>External</field><field>Feature</field></row></table><table name="WixProperty"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*36"><field>AUTOLAUNCHAPP</field><field>4</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*38"><field>LAUNCHAPPARGS</field><field>4</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*51"><field>ARPNOREPAIR</field><field>4</field></row></table><table name="WixSimpleReference"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*15"><field>Property</field><field>Manufacturer</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*15"><field>Property</field><field>ProductCode</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*15"><field>Property</field><field>ProductLanguage</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*15"><field>Property</field><field>ProductName</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*15"><field>Property</field><field>ProductVersion</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*15"><field>Property</field><field>UpgradeCode</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*52"><field>WixAction</field><field>InstallExecuteSequence/InstallValidate</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*66"><field>File</field><field>Path</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*70"><field>Dialog</field><field>ExitDialog</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*70"><field>CustomAction</field><field>LaunchApplication</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*75"><field>Dialog</field><field>WelcomeDlg</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*75"><field>Dialog</field><field>InstallDirDlg</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*80"><field>Dialog</field><field>InstallDirDlg</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*80"><field>Dialog</field><field>WelcomeDlg</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*87"><field>WixUI</field><field>WixUI_InstallDir</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*105"><field>Directory</field><field>INSTALLDIR</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*113"><field>Media</field><field>1</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*136"><field>Directory</field><field>ApplicationProgramsFolder</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*138"><field>Icon</field><field>ProductIcon</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*151"><field>Directory</field><field>INSTALLDIR</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*161"><field>Component</field><field>RegistryEntries</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*167"><field>Component</field><field>Path</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*168"><field>Component</field><field>CMP_UninstallShortcut</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*169"><field>Component</field><field>ApplicationShortcut</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*170"><field>Component</field><field>ApplicationShortcutDesktop</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*179"><field>Component</field><field>Path</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*192"><field>Directory</field><field>INSTALLDIR</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*194"><field>CustomAction</field><field>DownloadAndInvokeBootstrapper</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*194"><field>WixAction</field><field>InstallExecuteSequence/InstallFinalize</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*206"><field>CustomAction</field><field>LaunchApplication</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*206"><field>WixAction</field><field>InstallExecuteSequence/InstallFinalize</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*209"><field>WixAction</field><field>InstallUISequence/CostFinalize</field></row><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*209"><field>WixAction</field><field>InstallExecuteSequence/CostFinalize</field></row></table></section><section id="*.Manufacturer" type="fragment"><table name="Property"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*15"><field>Manufacturer</field><field>asterisk</field></row></table></section><section id="*.ProductCode" type="fragment"><table name="Property"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*15"><field>ProductCode</field><field>*</field></row></table></section><section id="*.ProductLanguage" type="fragment"><table name="Property"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*15"><field>ProductLanguage</field><field>!(loc.TauriLanguage)</field></row></table></section><section id="*.ProductName" type="fragment"><table name="Property"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*15"><field>ProductName</field><field>Claudia</field></row></table></section><section id="*.ProductVersion" type="fragment"><table name="Property"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*15"><field>ProductVersion</field><field>0.1.0</field></row></table></section><section id="*.UpgradeCode" type="fragment"><table name="Property"><row sourceLineNumber="C:\WorkSpace\claudia\src-tauri\target\release\wix\x64\main.wxs*15"><field>UpgradeCode</field><field>{49D01237-4D4C-5579-99AA-47B9BD03E500}</field></row></table></section></wixObject>