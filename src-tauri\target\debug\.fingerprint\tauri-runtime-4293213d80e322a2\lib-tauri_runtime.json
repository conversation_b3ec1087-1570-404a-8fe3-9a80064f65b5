{"rustc": 16531013993302481708, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 17092063984591198707, "deps": [[442785307232013896, "build_script_build", false, 11250649418887526953], [3150220818285335163, "url", false, 8382040255407682105], [4143744114649553716, "raw_window_handle", false, 7474244642556368010], [7606335748176206944, "dpi", false, 1855747390075338734], [9010263965687315507, "http", false, 12229602069060356524], [9689903380558560274, "serde", false, 498733323001508635], [10806645703491011684, "thiserror", false, 12848503974094196907], [11050281405049894993, "tauri_utils", false, 16322645452029113675], [13116089016666501665, "windows", false, 17833095620099237531], [15367738274754116744, "serde_json", false, 15963293933936563447], [16727543399706004146, "cookie", false, 12317123807546010569]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-4293213d80e322a2\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}