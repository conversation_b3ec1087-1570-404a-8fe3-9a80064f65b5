C:\WorkSpace\claudia\src-tauri\target\debug\deps\claudia_lib-f6763dcbe2b0de8b.d: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\sandbox.rs src\commands\screenshot.rs src\commands\usage.rs src\process\mod.rs src\process\registry.rs src\sandbox\mod.rs src\sandbox\defaults.rs src\sandbox\executor.rs src\sandbox\platform.rs src\sandbox\profile.rs C:\WorkSpace\claudia\src-tauri\target\debug\build\claudia-16adc0b6a62653dd\out/20afe343d7c4fc153b5ac7610b5f5855919c55ba1443764f679eca2c5e534736

C:\WorkSpace\claudia\src-tauri\target\debug\deps\libclaudia_lib-f6763dcbe2b0de8b.rmeta: src\lib.rs src\checkpoint\mod.rs src\checkpoint\manager.rs src\checkpoint\state.rs src\checkpoint\storage.rs src\claude_binary.rs src\commands\mod.rs src\commands\agents.rs src\commands\claude.rs src\commands\mcp.rs src\commands\sandbox.rs src\commands\screenshot.rs src\commands\usage.rs src\process\mod.rs src\process\registry.rs src\sandbox\mod.rs src\sandbox\defaults.rs src\sandbox\executor.rs src\sandbox\platform.rs src\sandbox\profile.rs C:\WorkSpace\claudia\src-tauri\target\debug\build\claudia-16adc0b6a62653dd\out/20afe343d7c4fc153b5ac7610b5f5855919c55ba1443764f679eca2c5e534736

src\lib.rs:
src\checkpoint\mod.rs:
src\checkpoint\manager.rs:
src\checkpoint\state.rs:
src\checkpoint\storage.rs:
src\claude_binary.rs:
src\commands\mod.rs:
src\commands\agents.rs:
src\commands\claude.rs:
src\commands\mcp.rs:
src\commands\sandbox.rs:
src\commands\screenshot.rs:
src\commands\usage.rs:
src\process\mod.rs:
src\process\registry.rs:
src\sandbox\mod.rs:
src\sandbox\defaults.rs:
src\sandbox\executor.rs:
src\sandbox\platform.rs:
src\sandbox\profile.rs:
C:\WorkSpace\claudia\src-tauri\target\debug\build\claudia-16adc0b6a62653dd\out/20afe343d7c4fc153b5ac7610b5f5855919c55ba1443764f679eca2c5e534736:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=claudia
# env-dep:OUT_DIR=C:\\WorkSpace\\claudia\\src-tauri\\target\\debug\\build\\claudia-16adc0b6a62653dd\\out
