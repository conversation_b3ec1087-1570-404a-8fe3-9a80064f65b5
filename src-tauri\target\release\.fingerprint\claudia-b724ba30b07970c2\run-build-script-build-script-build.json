{"rustc": 16531013993302481708, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9805105373369294601, "build_script_build", false, 15823340056089481623], [10755362358622467486, "build_script_build", false, 278435022346348165], [13919194856117907555, "build_script_build", false, 12047734493907998696], [3834743577069889284, "build_script_build", false, 8820378218312717110], [13890802266741835355, "build_script_build", false, 1304259251640270305], [246920333930397414, "build_script_build", false, 17471075025888350686], [15441187897486245138, "build_script_build", false, 14511608105974349044], [7849236192756901113, "build_script_build", false, 13539627951794035001], [17962022290347926134, "build_script_build", false, 671474234908455606], [1582828171158827377, "build_script_build", false, 17250982359118659330], [18440762029541581206, "build_script_build", false, 488797861534707135]], "local": [{"RerunIfChanged": {"output": "release\\build\\claudia-b724ba30b07970c2\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}