{"rustc": 16531013993302481708, "features": "[\"log\", \"logging\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 3644725415637297967, "path": 1432887025267605703, "deps": [[2883436298747778685, "pki_types", false, 10344219780024642299], [3722963349756955755, "once_cell", false, 5806442715378666544], [5491919304041016563, "ring", false, 7384804342421404241], [5986029879202738730, "log", false, 9966600394995078573], [6528079939221783635, "zeroize", false, 6003155934702933093], [16400140949089969347, "build_script_build", false, 6109220159167275768], [17003143334332120809, "subtle", false, 4484046506055549634], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 16359065930138394955]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rustls-67876bdba4e2b710\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}