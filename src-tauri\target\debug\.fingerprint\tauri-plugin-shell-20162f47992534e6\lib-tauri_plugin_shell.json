{"rustc": 16531013993302481708, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 15657897354478470176, "path": 12677275984968714397, "deps": [[500211409582349667, "shared_child", false, 13834851611740659136], [1582828171158827377, "build_script_build", false, 4379765858623735978], [5986029879202738730, "log", false, 9762708637302808169], [9451456094439810778, "regex", false, 9543725131571474675], [9538054652646069845, "tokio", false, 17887723141469624085], [9689903380558560274, "serde", false, 498733323001508635], [10755362358622467486, "tauri", false, 14358646239143990346], [10806645703491011684, "thiserror", false, 12848503974094196907], [11337703028400419576, "os_pipe", false, 17992311330560800110], [14564311161534545801, "encoding_rs", false, 5106615457713308014], [15367738274754116744, "serde_json", false, 15963293933936563447], [16192041687293812804, "open", false, 9308576107144698384]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-20162f47992534e6\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}