{"rustc": 16531013993302481708, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 14543361324211356615, "deps": [[376837177317575824, "softbuffer", false, 12760038925457866947], [442785307232013896, "tauri_runtime", false, 13775295086558129965], [3150220818285335163, "url", false, 8382040255407682105], [3722963349756955755, "once_cell", false, 2195721209096281828], [4143744114649553716, "raw_window_handle", false, 7474244642556368010], [5986029879202738730, "log", false, 9762708637302808169], [7752760652095876438, "build_script_build", false, 10340832120721247937], [8539587424388551196, "webview2_com", false, 13372110546693346305], [9010263965687315507, "http", false, 12229602069060356524], [11050281405049894993, "tauri_utils", false, 16322645452029113675], [13116089016666501665, "windows", false, 17833095620099237531], [13223659721939363523, "tao", false, 16839300867089497641], [14794439852947137341, "wry", false, 6216200756580424395]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-c3ec5458ddb3ef77\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}