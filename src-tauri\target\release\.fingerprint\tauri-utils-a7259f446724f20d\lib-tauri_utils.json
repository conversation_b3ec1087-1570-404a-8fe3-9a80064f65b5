{"rustc": 16531013993302481708, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 1369601567987815722, "path": 15871453406282790652, "deps": [[561782849581144631, "html5ever", false, 1673225708245142505], [1200537532907108615, "url<PERSON><PERSON>n", false, 15407461788838932818], [3060637413840920116, "proc_macro2", false, 4996313762018301561], [3129130049864710036, "memchr", false, 5314484071814711707], [3150220818285335163, "url", false, 2866603802530543272], [3191507132440681679, "serde_untagged", false, 16254583600623565609], [4899080583175475170, "semver", false, 4232567585220695663], [5986029879202738730, "log", false, 9966600394995078573], [6213549728662707793, "serde_with", false, 12594788099065774092], [6262254372177975231, "kuchiki", false, 12345000303290791348], [6606131838865521726, "ctor", false, 5436888814629660613], [6913375703034175521, "schemars", false, 15332617047553977941], [7170110829644101142, "json_patch", false, 16994542221297411569], [8319709847752024821, "uuid", false, 17392636067544197873], [9010263965687315507, "http", false, 1023215973640348034], [9451456094439810778, "regex", false, 13513286630159968979], [9689903380558560274, "serde", false, 5716194274910306086], [10806645703491011684, "thiserror", false, 17180542245805178611], [11655476559277113544, "cargo_metadata", false, 1035918541303537218], [11989259058781683633, "dunce", false, 13957091803875944881], [13625485746686963219, "anyhow", false, 9635726037522150273], [14132538657330703225, "brotli", false, 1105225599824512465], [15367738274754116744, "serde_json", false, 1889407351701900882], [15609422047640926750, "toml", false, 10109191835829690379], [15622660310229662834, "walkdir", false, 12081892974504724268], [17146114186171651583, "infer", false, 6397316230054510001], [17155886227862585100, "glob", false, 5353273545251755646], [17186037756130803222, "phf", false, 5777499080368217374], [17990358020177143287, "quote", false, 15312352581876860524]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-a7259f446724f20d\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}