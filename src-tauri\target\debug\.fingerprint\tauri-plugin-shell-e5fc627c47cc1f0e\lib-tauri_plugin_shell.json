{"rustc": 16531013993302481708, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 2241668132362809309, "path": 12677275984968714397, "deps": [[500211409582349667, "shared_child", false, 1310337146761726683], [1582828171158827377, "build_script_build", false, 5889760895741198783], [5986029879202738730, "log", false, 3108541596750279158], [9451456094439810778, "regex", false, 7385866613988980781], [9538054652646069845, "tokio", false, 11647163168096858975], [9689903380558560274, "serde", false, 1356923275834828457], [10755362358622467486, "tauri", false, 9659588977301275262], [10806645703491011684, "thiserror", false, 8844752790588992264], [11337703028400419576, "os_pipe", false, 7087219068346417722], [14564311161534545801, "encoding_rs", false, 179255464844472571], [15367738274754116744, "serde_json", false, 5032607260025433330], [16192041687293812804, "open", false, 8391209460974027431]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-e5fc627c47cc1f0e\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}