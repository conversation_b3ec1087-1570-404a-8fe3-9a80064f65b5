{"rustc": 16531013993302481708, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"http-range\", \"image\", \"image-png\", \"protocol-asset\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 9185822350802405798, "deps": [[40386456601120721, "percent_encoding", false, 9202197194377805677], [442785307232013896, "tauri_runtime", false, 11961418856536844731], [1200537532907108615, "url<PERSON><PERSON>n", false, 6718250427926080122], [3150220818285335163, "url", false, 9955794519733322107], [4143744114649553716, "raw_window_handle", false, 17998646994175285054], [4341921533227644514, "muda", false, 9002566832280831706], [4919829919303820331, "serialize_to_javascript", false, 997687310735273418], [5986029879202738730, "log", false, 15145306266573603605], [7752760652095876438, "tauri_runtime_wry", false, 13405256704460326450], [8351317599104215083, "tray_icon", false, 8873627200114687596], [8539587424388551196, "webview2_com", false, 7650905707168890062], [8866577183823226611, "http_range", false, 5972267218516261953], [9010263965687315507, "http", false, 11117590330939092863], [9228235415475680086, "tauri_macros", false, 17414398183279596395], [9538054652646069845, "tokio", false, 10685044774007314925], [9689903380558560274, "serde", false, 7565272720106024856], [9920160576179037441, "getrandom", false, 7799593937921196649], [10229185211513642314, "mime", false, 7293429583819305088], [10629569228670356391, "futures_util", false, 10080239264288247771], [10755362358622467486, "build_script_build", false, 278435022346348165], [10806645703491011684, "thiserror", false, 3262636517690463515], [11050281405049894993, "tauri_utils", false, 3463971423607272012], [11989259058781683633, "dunce", false, 11998082795202031044], [12565293087094287914, "window_vibrancy", false, 7392483470885104395], [12986574360607194341, "serde_repr", false, 15864135832470065171], [13028763805764736075, "image", false, 14470097973086014417], [13077543566650298139, "heck", false, 2679107103343802255], [13116089016666501665, "windows", false, 13890596246046135561], [13625485746686963219, "anyhow", false, 18019008218539212579], [15367738274754116744, "serde_json", false, 8303730601567159926], [16928111194414003569, "dirs", false, 4974210369899937964], [17155886227862585100, "glob", false, 938717186080104040]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-bc8cedde990b0aa4\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}