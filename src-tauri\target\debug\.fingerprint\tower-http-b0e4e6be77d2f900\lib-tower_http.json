{"rustc": 16531013993302481708, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 15657897354478470176, "path": 12237393844182399288, "deps": [[784494742817713399, "tower_service", false, 10329226803908331166], [1906322745568073236, "pin_project_lite", false, 3511936401832503266], [4121350475192885151, "iri_string", false, 9063921540812097867], [5695049318159433696, "tower", false, 2781537199415237319], [7712452662827335977, "tower_layer", false, 9342354063209956881], [7896293946984509699, "bitflags", false, 17330950875291285444], [9010263965687315507, "http", false, 12229602069060356524], [10629569228670356391, "futures_util", false, 5965820397420444232], [14084095096285906100, "http_body", false, 4275776705827243960], [16066129441945555748, "bytes", false, 1700372560425586026]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-b0e4e6be77d2f900\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}