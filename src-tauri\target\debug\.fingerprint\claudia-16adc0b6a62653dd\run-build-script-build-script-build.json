{"rustc": 16531013993302481708, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9805105373369294601, "build_script_build", false, 8120162404408030291], [10755362358622467486, "build_script_build", false, 16468743714951719541], [13919194856117907555, "build_script_build", false, 2267259500856298394], [3834743577069889284, "build_script_build", false, 1614227942262671779], [13890802266741835355, "build_script_build", false, 7595225595774099825], [246920333930397414, "build_script_build", false, 2733761452987474986], [15441187897486245138, "build_script_build", false, 15758603279151548936], [7849236192756901113, "build_script_build", false, 13385308901235046293], [17962022290347926134, "build_script_build", false, 4208723474038779481], [1582828171158827377, "build_script_build", false, 5889760895741198783], [18440762029541581206, "build_script_build", false, 8480512896917738160]], "local": [{"RerunIfChanged": {"output": "debug\\build\\claudia-16adc0b6a62653dd\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}