{"rustc": 16531013993302481708, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2040997289075261528, "path": 15670307905118783676, "deps": [[1573238666360410412, "rand_chacha", false, 9508261120493049294], [18130209639506977569, "rand_core", false, 4387205785756904263]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rand-b3931c54bc835d89\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}