{"rustc": 16531013993302481708, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 7795770796983219439, "profile": 2040997289075261528, "path": 15281685881539096402, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 6718250427926080122], [3150220818285335163, "url", false, 9955794519733322107], [8218178811151724123, "reqwest", false, 15497454212521049589], [8298091525883606470, "cookie_store", false, 7375428158574983772], [9010263965687315507, "http", false, 11117590330939092863], [9451456094439810778, "regex", false, 18184761356493868869], [9538054652646069845, "tokio", false, 10685044774007314925], [9689903380558560274, "serde", false, 7565272720106024856], [10755362358622467486, "tauri", false, 17824018193451593779], [10806645703491011684, "thiserror", false, 3262636517690463515], [13890802266741835355, "tauri_plugin_fs", false, 10193204064736892491], [15367738274754116744, "serde_json", false, 8303730601567159926], [15441187897486245138, "build_script_build", false, 14511608105974349044], [16066129441945555748, "bytes", false, 16809627282366868376], [17047088963840213854, "data_url", false, 10656116706848214087]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-http-8a4a1fb4b915f66c\\dep-lib-tauri_plugin_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}