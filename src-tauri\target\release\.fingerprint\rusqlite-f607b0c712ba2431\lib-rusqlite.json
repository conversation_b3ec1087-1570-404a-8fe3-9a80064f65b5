{"rustc": 16531013993302481708, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"column_metadata\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"jiff\", \"limits\", \"load_extension\", \"loadable_extension\", \"modern-full\", \"modern_sqlite\", \"preupdate_hook\", \"rusqlite-macros\", \"serde_json\", \"serialize\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"with-asan\"]", "target": 10662205063260755052, "profile": 2040997289075261528, "path": 12243049074312574588, "deps": [[697300053061991528, "libsqlite3_sys", false, 334353330318543313], [1303438375223863970, "hashlink", false, 16186896261535164072], [3666196340704888985, "smallvec", false, 8521332158414776124], [5510864063823219921, "fallible_streaming_iterator", false, 12954273563473859262], [7896293946984509699, "bitflags", false, 5252783183867672309], [12860549049674006569, "fallible_iterator", false, 14796530430389819817]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rusqlite-f607b0c712ba2431\\dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}