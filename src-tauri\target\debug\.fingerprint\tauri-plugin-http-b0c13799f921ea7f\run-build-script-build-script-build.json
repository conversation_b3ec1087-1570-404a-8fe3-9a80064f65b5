{"rustc": 16531013993302481708, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 16468743714951719541], [13890802266741835355, "build_script_build", false, 7595225595774099825], [15441187897486245138, "build_script_build", false, 9057587925912982972]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-http-b0c13799f921ea7f\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}