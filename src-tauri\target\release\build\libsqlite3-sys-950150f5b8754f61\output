cargo:rerun-if-env-changed=LIBSQLITE3_SYS_USE_PKG_CONFIG
cargo:include=C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\libsqlite3-sys-0.34.0/sqlite3
cargo:rerun-if-changed=sqlite3/sqlite3.c
cargo:rerun-if-changed=sqlite3/wasm32-wasi-vfs.c
cargo:rerun-if-env-changed=SQLITE_MAX_VARIABLE_NUMBER
cargo:rerun-if-env-changed=SQLITE_MAX_EXPR_DEPTH
cargo:rerun-if-env-changed=SQLITE_MAX_COLUMN
cargo:rerun-if-env-changed=LIBSQLITE3_FLAGS
OUT_DIR = Some(C:\WorkSpace\claudia\src-tauri\target\release\build\libsqlite3-sys-950150f5b8754f61\out)
OPT_LEVEL = Some(3)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(C:\WorkSpace\claudia\src-tauri\target\release\deps;C:\WorkSpace\claudia\src-tauri\target\release;C:\Users\<USER>\.rustup\toolchains\nightly-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;C:\WorkSpace\claudia\node_modules\.bin;C:\WorkSpace\claudia\node_modules\.bin;C:\WorkSpace\node_modules\.bin;C:\node_modules\.bin;C:\Program Files (x86)\Embarcadero\Studio\23.0\bin;C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Bpl;C:\Program Files (x86)\Embarcadero\Studio\23.0\bin64;C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Bpl\Win64;C:\Python313\Scripts\;C:\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\Embarcadero\Studio\22.0\bin;C:\Users\<USER>\Documents\Embarcadero\Studio\22.0\Bpl;C:\Program Files (x86)\Embarcadero\Studio\22.0\bin64;C:\Users\<USER>\Documents\Embarcadero\Studio\22.0\Bpl\Win64;C:\Python311-32\Scripts\;C:\Python311-32\;C:\Python311-64\Scripts\;C:\Python311-64\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Bandizip\;C:\mingw64\bin;C:\Users\<USER>\.cargo\bin;C:\anaconda3;C:\anaconda3\Library\mingw-w64\bin;C:\anaconda3\Library\usr\bin;C:\anaconda3\Library\bin;C:\anaconda3\Scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\ffmpeg\bin;C:\Program Files\Zero Install;C:\MinGW\bin;C:\MinGW\msys\1.0\bin;C:\Strawberry\c\bin;C:\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Program Files\cursor\resources\app\bin;C:\Program Files\Go\bin;C:\Program Files\Git\cmd;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\scoop\shims;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\.local\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Qt\5.14.0\msvc2017_64\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code Insiders\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows\SysWow64\;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\go\bin;C:\Users\<USER>\.rustup\toolchains\nightly-x86_64-pc-windows-msvc\bin)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,lahfsahf,sse,sse2,sse3,x87)
DEBUG = Some(false)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
sqlite3.c
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-lib=static=sqlite3
cargo:rustc-link-search=native=C:\WorkSpace\claudia\src-tauri\target\release\build\libsqlite3-sys-950150f5b8754f61\out
cargo:lib_dir=C:\WorkSpace\claudia\src-tauri\target\release\build\libsqlite3-sys-950150f5b8754f61\out
