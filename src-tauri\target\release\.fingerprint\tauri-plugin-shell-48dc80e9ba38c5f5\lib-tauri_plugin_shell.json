{"rustc": 16531013993302481708, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 2040997289075261528, "path": 12677275984968714397, "deps": [[500211409582349667, "shared_child", false, 8320151735900887983], [1582828171158827377, "build_script_build", false, 17250982359118659330], [5986029879202738730, "log", false, 15145306266573603605], [9451456094439810778, "regex", false, 18184761356493868869], [9538054652646069845, "tokio", false, 10685044774007314925], [9689903380558560274, "serde", false, 7565272720106024856], [10755362358622467486, "tauri", false, 17824018193451593779], [10806645703491011684, "thiserror", false, 3262636517690463515], [11337703028400419576, "os_pipe", false, 6554266565344731992], [14564311161534545801, "encoding_rs", false, 9323012540895115995], [15367738274754116744, "serde_json", false, 8303730601567159926], [16192041687293812804, "open", false, 17757843408333234930]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-shell-48dc80e9ba38c5f5\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}