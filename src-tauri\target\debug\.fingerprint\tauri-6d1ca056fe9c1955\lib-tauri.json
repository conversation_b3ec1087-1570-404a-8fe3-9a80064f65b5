{"rustc": 16531013993302481708, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"http-range\", \"image\", \"image-png\", \"protocol-asset\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 9185822350802405798, "deps": [[40386456601120721, "percent_encoding", false, 6102219559593510735], [442785307232013896, "tauri_runtime", false, 13775295086558129965], [1200537532907108615, "url<PERSON><PERSON>n", false, 8099047492367206136], [3150220818285335163, "url", false, 8382040255407682105], [4143744114649553716, "raw_window_handle", false, 7474244642556368010], [4341921533227644514, "muda", false, 8635309928880241302], [4919829919303820331, "serialize_to_javascript", false, 16509889228315922449], [5986029879202738730, "log", false, 9762708637302808169], [7752760652095876438, "tauri_runtime_wry", false, 17963221939585666627], [8351317599104215083, "tray_icon", false, 840793331501170366], [8539587424388551196, "webview2_com", false, 13372110546693346305], [8866577183823226611, "http_range", false, 3799014290945709652], [9010263965687315507, "http", false, 12229602069060356524], [9228235415475680086, "tauri_macros", false, 8832212922269971432], [9538054652646069845, "tokio", false, 17887723141469624085], [9689903380558560274, "serde", false, 498733323001508635], [9920160576179037441, "getrandom", false, 9251902749533107122], [10229185211513642314, "mime", false, 13228290812740384226], [10629569228670356391, "futures_util", false, 5965820397420444232], [10755362358622467486, "build_script_build", false, 12218645810558505789], [10806645703491011684, "thiserror", false, 12848503974094196907], [11050281405049894993, "tauri_utils", false, 16322645452029113675], [11989259058781683633, "dunce", false, 9398898499214381831], [12565293087094287914, "window_vibrancy", false, 9740372446204745599], [12986574360607194341, "serde_repr", false, 18191837527263456998], [13028763805764736075, "image", false, 15550048071724996262], [13077543566650298139, "heck", false, 13003241475100688398], [13116089016666501665, "windows", false, 17833095620099237531], [13625485746686963219, "anyhow", false, 5661029371983558191], [15367738274754116744, "serde_json", false, 15963293933936563447], [16928111194414003569, "dirs", false, 12624433838666671492], [17155886227862585100, "glob", false, 16471218984395200217]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-6d1ca056fe9c1955\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}