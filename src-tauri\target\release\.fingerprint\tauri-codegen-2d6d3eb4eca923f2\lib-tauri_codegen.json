{"rustc": 16531013993302481708, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 1369601567987815722, "path": 170688056642803938, "deps": [[3060637413840920116, "proc_macro2", false, 4996313762018301561], [3150220818285335163, "url", false, 2866603802530543272], [4899080583175475170, "semver", false, 4232567585220695663], [7170110829644101142, "json_patch", false, 16994542221297411569], [7392050791754369441, "ico", false, 9951966761075143850], [8319709847752024821, "uuid", false, 17392636067544197873], [9689903380558560274, "serde", false, 5716194274910306086], [9857275760291862238, "sha2", false, 5480142456884700873], [10806645703491011684, "thiserror", false, 17180542245805178611], [11050281405049894993, "tauri_utils", false, 8794795821054681640], [12687914511023397207, "png", false, 5457370928790095960], [13077212702700853852, "base64", false, 17713193311424426127], [14132538657330703225, "brotli", false, 1105225599824512465], [15367738274754116744, "serde_json", false, 1889407351701900882], [15622660310229662834, "walkdir", false, 12081892974504724268], [17990358020177143287, "quote", false, 15312352581876860524], [18149961000318489080, "syn", false, 17063894373588389093]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-2d6d3eb4eca923f2\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}