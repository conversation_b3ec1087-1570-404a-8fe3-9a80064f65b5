{"rustc": 16531013993302481708, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 12218645810558505789], [13890802266741835355, "build_script_build", false, 8033386978985984147], [15441187897486245138, "build_script_build", false, 17520732569591435118]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-http-39b05afd48e7b3cd\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}