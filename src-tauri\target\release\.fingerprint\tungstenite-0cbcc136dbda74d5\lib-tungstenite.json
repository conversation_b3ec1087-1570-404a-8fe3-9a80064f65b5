{"rustc": 16531013993302481708, "features": "[\"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"sha1\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 5395530797274129873, "profile": 2040997289075261528, "path": 4804470596962136781, "deps": [[99287295355353247, "data_encoding", false, 12364401512186524216], [2098583196738611028, "rand", false, 15784315190774804003], [4359956005902820838, "utf8", false, 14521092920878509288], [5986029879202738730, "log", false, 15145306266573603605], [6163892036024256188, "httparse", false, 5263097600830553608], [9010263965687315507, "http", false, 11117590330939092863], [10724389056617919257, "sha1", false, 12642631975762119905], [10806645703491011684, "thiserror", false, 3262636517690463515], [16066129441945555748, "bytes", false, 16809627282366868376]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tungstenite-0cbcc136dbda74d5\\dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}