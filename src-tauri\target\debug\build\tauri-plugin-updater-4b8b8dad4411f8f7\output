cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=C:\WorkSpace\claudia\src-tauri\target\debug\build\tauri-plugin-updater-4b8b8dad4411f8f7\out\tauri-plugin-updater-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-updater-2.8.1\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
