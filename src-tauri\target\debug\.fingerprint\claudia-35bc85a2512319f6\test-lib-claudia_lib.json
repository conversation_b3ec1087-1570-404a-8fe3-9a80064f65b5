{"rustc": 16531013993302481708, "features": "[]", "declared_features": "[\"custom-protocol\"]", "target": 4094945128430846471, "profile": 3316208278650011218, "path": 10763286916239946207, "deps": [[246920333930397414, "tauri_plugin_global_shortcut", false, 8254043753254166597], [1441306149310335789, "tempfile", false, 13784350345834353687], [1582828171158827377, "tauri_plugin_shell", false, 8026578603817760817], [2186951887354108979, "which", false, 1643970041401253999], [2706460456408817945, "futures", false, 18110269316645533230], [2924422107542798392, "libc", false, 4433310375972968476], [3834743577069889284, "tauri_plugin_dialog", false, 8272666566763973847], [4052408954973158025, "zstd", false, 16304429581566987969], [5986029879202738730, "log", false, 3108541596750279158], [6898646762435821041, "env_logger", false, 4376712450620622962], [7849236192756901113, "tauri_plugin_notification", false, 12588742138188070483], [8218178811151724123, "reqwest", false, 15328367791607731781], [8319709847752024821, "uuid", false, 7445519552951018437], [9451456094439810778, "regex", false, 7385866613988980781], [9538054652646069845, "tokio", false, 11647163168096858975], [9689903380558560274, "serde", false, 1356923275834828457], [9805105373369294601, "build_script_build", false, 14146940702412045511], [9857275760291862238, "sha2", false, 11405490881699991903], [9897246384292347999, "chrono", false, 6247052771252306255], [10755362358622467486, "tauri", false, 9659588977301275262], [11946729385090170470, "async_trait", false, 1522689324471784288], [13077212702700853852, "base64", false, 17473034184759788966], [13625485746686963219, "anyhow", false, 14958058165012313471], [13890802266741835355, "tauri_plugin_fs", false, 556532965761531288], [13919194856117907555, "tauri_plugin_clipboard_manager", false, 2648820125166527984], [14843943393121946134, "headless_chrome", false, 4665039719440801165], [15367738274754116744, "serde_json", false, 5032607260025433330], [15441187897486245138, "tauri_plugin_http", false, 7545312904423902838], [15622660310229662834, "walkdir", false, 9748913858653519688], [16829503058829197905, "rusqlite", false, 17532587264851206782], [16928111194414003569, "dirs", false, 10291156319872330776], [17155886227862585100, "glob", false, 17585169469182333978], [17962022290347926134, "tauri_plugin_process", false, 17315753423507435735], [18440762029541581206, "tauri_plugin_updater", false, 6674909747174127178]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\claudia-35bc85a2512319f6\\dep-test-lib-claudia_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}