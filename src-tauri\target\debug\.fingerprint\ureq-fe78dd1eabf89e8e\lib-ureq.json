{"rustc": 16531013993302481708, "features": "[\"_ring\", \"_rustls\", \"_tls\", \"default\", \"gzip\", \"rustls\", \"rustls-no-provider\"]", "declared_features": "[\"_doc\", \"_ring\", \"_rustls\", \"_test\", \"_tls\", \"_url\", \"brotli\", \"charset\", \"cookies\", \"default\", \"gzip\", \"json\", \"native-tls\", \"platform-verifier\", \"rustls\", \"rustls-no-provider\", \"socks-proxy\", \"vendored\"]", "target": 2636997325719059094, "profile": 15657897354478470176, "path": 7949152366572818604, "deps": [[40386456601120721, "percent_encoding", false, 6102219559593510735], [103671602955413300, "ureq_proto", false, 12682985618129202961], [2883436298747778685, "rustls_pki_types", false, 16857439300119542113], [4359956005902820838, "utf8", false, 5539610298539579672], [5986029879202738730, "log", false, 9762708637302808169], [8156804143951879168, "webpki_roots", false, 18165623615331300266], [13077212702700853852, "base64", false, 7522608780807098969], [15032952994102373905, "rustls_pemfile", false, 16421538805415311224], [16400140949089969347, "rustls", false, 849824138720431209], [17772299992546037086, "flate2", false, 16640786739204602412]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ureq-fe78dd1eabf89e8e\\dep-lib-ureq", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}