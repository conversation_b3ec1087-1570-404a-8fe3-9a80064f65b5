{"rustc": 16531013993302481708, "features": "[]", "declared_features": "[\"custom-protocol\"]", "target": 17604022209686676412, "profile": 2040997289075261528, "path": 4942398508502643691, "deps": [[246920333930397414, "tauri_plugin_global_shortcut", false, 12531130335041732162], [1441306149310335789, "tempfile", false, 10686609350612026450], [1582828171158827377, "tauri_plugin_shell", false, 17968481006338892081], [2186951887354108979, "which", false, 1845151036424408396], [2706460456408817945, "futures", false, 2685945701025228341], [2924422107542798392, "libc", false, 5395819242390708580], [3834743577069889284, "tauri_plugin_dialog", false, 16014580954856770010], [4052408954973158025, "zstd", false, 8104095011753940490], [5986029879202738730, "log", false, 15145306266573603605], [6898646762435821041, "env_logger", false, 4528021223267686489], [7849236192756901113, "tauri_plugin_notification", false, 871064893001708578], [8218178811151724123, "reqwest", false, 15497454212521049589], [8319709847752024821, "uuid", false, 114743279593149165], [9451456094439810778, "regex", false, 18184761356493868869], [9538054652646069845, "tokio", false, 10685044774007314925], [9689903380558560274, "serde", false, 7565272720106024856], [9805105373369294601, "claudia_lib", false, 6535156594015653333], [9805105373369294601, "build_script_build", false, 8540122083580341563], [9857275760291862238, "sha2", false, 2307966819351725048], [9897246384292347999, "chrono", false, 17110907973743976694], [10755362358622467486, "tauri", false, 17824018193451593779], [11946729385090170470, "async_trait", false, 12078957792346331897], [13077212702700853852, "base64", false, 16466660244743038906], [13625485746686963219, "anyhow", false, 18019008218539212579], [13890802266741835355, "tauri_plugin_fs", false, 10193204064736892491], [13919194856117907555, "tauri_plugin_clipboard_manager", false, 14768286308172194141], [14843943393121946134, "headless_chrome", false, 9910222433635503263], [15367738274754116744, "serde_json", false, 8303730601567159926], [15441187897486245138, "tauri_plugin_http", false, 10995926656529122309], [15622660310229662834, "walkdir", false, 1197033589297995617], [16829503058829197905, "rusqlite", false, 7991057675158589174], [16928111194414003569, "dirs", false, 4974210369899937964], [17155886227862585100, "glob", false, 938717186080104040], [17962022290347926134, "tauri_plugin_process", false, 6658680567857191376], [18440762029541581206, "tauri_plugin_updater", false, 15599400257611836663]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\claudia-d1df46e427e7ee26\\dep-bin-claudia", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}