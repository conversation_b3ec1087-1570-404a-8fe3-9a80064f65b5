{"rustc": 16531013993302481708, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 1369601567987815722, "path": 12997780948073429937, "deps": [[4899080583175475170, "semver", false, 4232567585220695663], [6913375703034175521, "schemars", false, 15332617047553977941], [7170110829644101142, "json_patch", false, 16994542221297411569], [9689903380558560274, "serde", false, 5716194274910306086], [11050281405049894993, "tauri_utils", false, 8794795821054681640], [12714016054753183456, "tauri_winres", false, 12446994461062011850], [13077543566650298139, "heck", false, 8308158770648476940], [13475171727366188400, "cargo_toml", false, 16089335172070941972], [13625485746686963219, "anyhow", false, 9635726037522150273], [15367738274754116744, "serde_json", false, 1889407351701900882], [15609422047640926750, "toml", false, 10109191835829690379], [15622660310229662834, "walkdir", false, 12081892974504724268], [16928111194414003569, "dirs", false, 12535920350511790096], [17155886227862585100, "glob", false, 5353273545251755646]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-build-1f829929f806320c\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}