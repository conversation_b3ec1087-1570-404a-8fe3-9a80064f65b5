{"rustc": 16531013993302481708, "features": "[\"log\", \"logging\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1677134433092527515, "path": 1432887025267605703, "deps": [[2883436298747778685, "pki_types", false, 16857439300119542113], [3722963349756955755, "once_cell", false, 2195721209096281828], [5491919304041016563, "ring", false, 15522370293152926080], [5986029879202738730, "log", false, 9762708637302808169], [6528079939221783635, "zeroize", false, 4886045614589346401], [16400140949089969347, "build_script_build", false, 14552779900795618477], [17003143334332120809, "subtle", false, 9907585617430565015], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 12173114072206008957]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-1bd8c67938f073bb\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}