{"rustc": 16531013993302481708, "features": "[\"default\", \"gzip\", \"socks-proxy\", \"tls\"]", "declared_features": "[\"brotli\", \"charset\", \"cookies\", \"default\", \"gzip\", \"http-crate\", \"http-interop\", \"json\", \"native-certs\", \"native-tls\", \"proxy-from-env\", \"socks-proxy\", \"testdeps\", \"tls\"]", "target": 2636997325719059094, "profile": 2225463790103693989, "path": 16420331238004747001, "deps": [[1129418674938548854, "socks", false, 11715621790146548752], [2883436298747778685, "rustls_pki_types", false, 6252725707407500683], [3150220818285335163, "url", false, 6102992118273930879], [3722963349756955755, "once_cell", false, 2195721209096281828], [5986029879202738730, "log", false, 16717307215505850697], [8156804143951879168, "webpki_roots", false, 18421987514063448429], [13077212702700853852, "base64", false, 7522608780807098969], [16400140949089969347, "rustls", false, 18019243389982489327], [17772299992546037086, "flate2", false, 16640786739204602412]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ureq-8f68f2ab374a3c9e\\dep-lib-ureq", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}