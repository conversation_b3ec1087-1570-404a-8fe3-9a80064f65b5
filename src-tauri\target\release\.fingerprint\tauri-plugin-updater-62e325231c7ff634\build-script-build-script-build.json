{"rustc": 16531013993302481708, "features": "[\"default\", \"rustls-tls\", \"zip\"]", "declared_features": "[\"default\", \"native-tls\", \"native-tls-vendored\", \"rustls-tls\", \"zip\"]", "target": 5408242616063297496, "profile": 1369601567987815722, "path": 2427601551842899375, "deps": [[2326493920556799156, "tauri_plugin", false, 4768064431530170862]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-updater-62e325231c7ff634\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}