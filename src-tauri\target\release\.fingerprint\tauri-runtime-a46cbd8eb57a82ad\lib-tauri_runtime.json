{"rustc": 16531013993302481708, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2040997289075261528, "path": 17092063984591198707, "deps": [[442785307232013896, "build_script_build", false, 9762918315922262510], [3150220818285335163, "url", false, 9955794519733322107], [4143744114649553716, "raw_window_handle", false, 17998646994175285054], [7606335748176206944, "dpi", false, 3311268853421474943], [9010263965687315507, "http", false, 11117590330939092863], [9689903380558560274, "serde", false, 7565272720106024856], [10806645703491011684, "thiserror", false, 3262636517690463515], [11050281405049894993, "tauri_utils", false, 3463971423607272012], [13116089016666501665, "windows", false, 13890596246046135561], [15367738274754116744, "serde_json", false, 8303730601567159926], [16727543399706004146, "cookie", false, 15309766727643194656]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-a46cbd8eb57a82ad\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}