{"rustc": 16531013993302481708, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 12841763714740291134, "deps": [[3060637413840920116, "proc_macro2", false, 4996313762018301561], [7341521034400937459, "tauri_codegen", false, 9362512909919643448], [11050281405049894993, "tauri_utils", false, 8794795821054681640], [13077543566650298139, "heck", false, 8308158770648476940], [17990358020177143287, "quote", false, 15312352581876860524], [18149961000318489080, "syn", false, 17063894373588389093]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-1057651c29ac61c5\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}