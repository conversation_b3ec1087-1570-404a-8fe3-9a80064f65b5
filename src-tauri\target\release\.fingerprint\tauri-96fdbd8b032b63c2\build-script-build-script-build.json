{"rustc": 16531013993302481708, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"http-range\", \"image\", \"image-png\", \"protocol-asset\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 5408242616063297496, "profile": 1369601567987815722, "path": 334727996535016738, "deps": [[283161442353679854, "tauri_build", false, 4398849235018719753], [11050281405049894993, "tauri_utils", false, 8794795821054681640], [13077543566650298139, "heck", false, 8308158770648476940], [17155886227862585100, "glob", false, 5353273545251755646]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-96fdbd8b032b63c2\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}