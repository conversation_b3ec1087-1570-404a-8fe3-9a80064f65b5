{"rustc": 16531013993302481708, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 14543361324211356615, "deps": [[376837177317575824, "softbuffer", false, 18442729232998553139], [442785307232013896, "tauri_runtime", false, 11961418856536844731], [3150220818285335163, "url", false, 9955794519733322107], [3722963349756955755, "once_cell", false, 9905004071064053928], [4143744114649553716, "raw_window_handle", false, 17998646994175285054], [5986029879202738730, "log", false, 15145306266573603605], [7752760652095876438, "build_script_build", false, 16749372151412128922], [8539587424388551196, "webview2_com", false, 7650905707168890062], [9010263965687315507, "http", false, 11117590330939092863], [11050281405049894993, "tauri_utils", false, 3463971423607272012], [13116089016666501665, "windows", false, 13890596246046135561], [13223659721939363523, "tao", false, 8592063103362769225], [14794439852947137341, "wry", false, 11879585925273538339]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-8e2913e2bec7ef91\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}