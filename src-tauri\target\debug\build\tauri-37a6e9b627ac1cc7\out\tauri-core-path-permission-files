["\\\\?\\C:\\WorkSpace\\claudia\\src-tauri\\target\\debug\\build\\tauri-37a6e9b627ac1cc7\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\C:\\WorkSpace\\claudia\\src-tauri\\target\\debug\\build\\tauri-37a6e9b627ac1cc7\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\C:\\WorkSpace\\claudia\\src-tauri\\target\\debug\\build\\tauri-37a6e9b627ac1cc7\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\C:\\WorkSpace\\claudia\\src-tauri\\target\\debug\\build\\tauri-37a6e9b627ac1cc7\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\C:\\WorkSpace\\claudia\\src-tauri\\target\\debug\\build\\tauri-37a6e9b627ac1cc7\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\C:\\WorkSpace\\claudia\\src-tauri\\target\\debug\\build\\tauri-37a6e9b627ac1cc7\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\C:\\WorkSpace\\claudia\\src-tauri\\target\\debug\\build\\tauri-37a6e9b627ac1cc7\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\C:\\WorkSpace\\claudia\\src-tauri\\target\\debug\\build\\tauri-37a6e9b627ac1cc7\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\C:\\WorkSpace\\claudia\\src-tauri\\target\\debug\\build\\tauri-37a6e9b627ac1cc7\\out\\permissions\\path\\autogenerated\\default.toml"]