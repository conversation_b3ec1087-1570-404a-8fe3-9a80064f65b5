cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=C:\WorkSpace\claudia\src-tauri\target\debug\build\tauri-plugin-global-shortcut-431430c3d2b8a6eb\out\tauri-plugin-global-shortcut-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-global-shortcut-2.2.1\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
