{"rustc": 16531013993302481708, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 15657897354478470176, "path": 4420037543310468217, "deps": [[1462335029370885857, "quick_xml", false, 2214094655962235016], [3334271191048661305, "windows_version", false, 10488143127867307600], [10806645703491011684, "thiserror", false, 12848503974094196907], [13116089016666501665, "windows", false, 17833095620099237531]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winrt-notification-4e16815b66e7f4ba\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}