{"rustc": 16531013993302481708, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 15302557990823967831, "path": 7088080666952519920, "deps": [[40386456601120721, "percent_encoding", false, 6102219559593510735], [418947936956741439, "h2", false, 15587260035771537795], [778154619793643451, "hyper_util", false, 8700261575005116645], [784494742817713399, "tower_service", false, 10329226803908331166], [1288403060204016458, "tokio_util", false, 11448728050035368592], [1788832197870803419, "hyper_rustls", false, 169039663545660829], [1906322745568073236, "pin_project_lite", false, 3511936401832503266], [2054153378684941554, "tower_http", false, 13873732244902441335], [2517136641825875337, "sync_wrapper", false, 15172159588930250913], [2883436298747778685, "rustls_pki_types", false, 16857439300119542113], [3150220818285335163, "url", false, 8382040255407682105], [5695049318159433696, "tower", false, 2781537199415237319], [5907992341687085091, "webpki_roots", false, 2234376432967623493], [5986029879202738730, "log", false, 9762708637302808169], [7620660491849607393, "futures_core", false, 4969804832433720117], [8298091525883606470, "cookie_store", false, 5651138308444040432], [9010263965687315507, "http", false, 12229602069060356524], [9538054652646069845, "tokio", false, 17887723141469624085], [9689903380558560274, "serde", false, 498733323001508635], [10229185211513642314, "mime", false, 13228290812740384226], [10629569228670356391, "futures_util", false, 5965820397420444232], [11895591994124935963, "tokio_rustls", false, 15158511064436920642], [11957360342995674422, "hyper", false, 2167207615499343613], [12186126227181294540, "tokio_native_tls", false, 3900110991139315594], [13077212702700853852, "base64", false, 7522608780807098969], [14084095096285906100, "http_body", false, 4275776705827243960], [14564311161534545801, "encoding_rs", false, 5106615457713308014], [15367738274754116744, "serde_json", false, 15963293933936563447], [16066129441945555748, "bytes", false, 1700372560425586026], [16400140949089969347, "rustls", false, 849824138720431209], [16542808166767769916, "serde_urlencoded", false, 14270199959753430102], [16727543399706004146, "cookie_crate", false, 12317123807546010569], [16785601910559813697, "native_tls_crate", false, 18099155920907945438], [16900715236047033623, "http_body_util", false, 15039763841889800130], [18273243456331255970, "hyper_tls", false, 15421798455853150631]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-c1854a7b318ebd3b\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}