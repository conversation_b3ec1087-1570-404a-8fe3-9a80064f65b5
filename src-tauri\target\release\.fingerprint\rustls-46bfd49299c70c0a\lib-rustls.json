{"rustc": 16531013993302481708, "features": "[\"log\", \"logging\", \"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1988539120246850232, "path": 1432887025267605703, "deps": [[2883436298747778685, "pki_types", false, 2539368524067777037], [3722963349756955755, "once_cell", false, 9905004071064053928], [5491919304041016563, "ring", false, 1681379749442315826], [5986029879202738730, "log", false, 15145306266573603605], [6528079939221783635, "zeroize", false, 10817625566340352166], [16400140949089969347, "build_script_build", false, 9301414165418557362], [17003143334332120809, "subtle", false, 2934369699155788904], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 15133053243614604827]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rustls-46bfd49299c70c0a\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}