{"rustc": 16531013993302481708, "features": "[\"_ring\", \"_rustls\", \"_tls\", \"default\", \"gzip\", \"rustls\", \"rustls-no-provider\"]", "declared_features": "[\"_doc\", \"_ring\", \"_rustls\", \"_test\", \"_tls\", \"_url\", \"brotli\", \"charset\", \"cookies\", \"default\", \"gzip\", \"json\", \"native-tls\", \"platform-verifier\", \"rustls\", \"rustls-no-provider\", \"socks-proxy\", \"vendored\"]", "target": 2636997325719059094, "profile": 2040997289075261528, "path": 7949152366572818604, "deps": [[40386456601120721, "percent_encoding", false, 9202197194377805677], [103671602955413300, "ureq_proto", false, 6680202985727332427], [2883436298747778685, "rustls_pki_types", false, 2539368524067777037], [4359956005902820838, "utf8", false, 14521092920878509288], [5986029879202738730, "log", false, 15145306266573603605], [8156804143951879168, "webpki_roots", false, 5826277526716212915], [13077212702700853852, "base64", false, 16466660244743038906], [15032952994102373905, "rustls_pemfile", false, 10835585206079746351], [16400140949089969347, "rustls", false, 8718200407412011937], [17772299992546037086, "flate2", false, 9651969952382754387]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\ureq-bef05f15221bb823\\dep-lib-ureq", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}