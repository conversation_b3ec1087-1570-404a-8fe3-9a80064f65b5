{"rustc": 16531013993302481708, "features": "[]", "declared_features": "[]", "target": 13151518555585256095, "profile": 2040997289075261528, "path": 4420037543310468217, "deps": [[1462335029370885857, "quick_xml", false, 5669426915432996899], [3334271191048661305, "windows_version", false, 17247819634238932523], [10806645703491011684, "thiserror", false, 3262636517690463515], [13116089016666501665, "windows", false, 13890596246046135561]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-winrt-notification-2b5ed9a22f1bac98\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}