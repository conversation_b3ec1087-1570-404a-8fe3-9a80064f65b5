{"rustc": 16531013993302481708, "features": "[\"http1\", \"http2\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 2040997289075261528, "path": 18181316325815919262, "deps": [[778154619793643451, "hyper_util", false, 7443359069071197265], [784494742817713399, "tower_service", false, 13615595949129068892], [2883436298747778685, "pki_types", false, 2539368524067777037], [5907992341687085091, "webpki_roots", false, 9943752050360798102], [9010263965687315507, "http", false, 11117590330939092863], [9538054652646069845, "tokio", false, 10685044774007314925], [11895591994124935963, "tokio_rustls", false, 15329112768603493888], [11957360342995674422, "hyper", false, 3978426371719433608], [16400140949089969347, "rustls", false, 8718200407412011937]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hyper-rustls-6daebf6c61a66b83\\dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}