{"rustc": 16531013993302481708, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 18348216721672176038, "path": 17020629835262555241, "deps": [[5103565458935487, "futures_io", false, 10440764407985409969], [1811549171721445101, "futures_channel", false, 14102328368897460069], [7013762810557009322, "futures_sink", false, 11395111882479475514], [7620660491849607393, "futures_core", false, 11308563171548495787], [10629569228670356391, "futures_util", false, 10080239264288247771], [12779779637805422465, "futures_executor", false, 11904016155435215733], [16240732885093539806, "futures_task", false, 8025370592399166534]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-48146b8b84789834\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}