{"rustc": 16531013993302481708, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 5408242616063297496, "profile": 1369601567987815722, "path": 12677452500306300857, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 15407461788838932818], [2326493920556799156, "tauri_plugin", false, 4768064431530170862], [3150220818285335163, "url", false, 2866603802530543272], [6913375703034175521, "schemars", false, 15332617047553977941], [9451456094439810778, "regex", false, 13513286630159968979], [9689903380558560274, "serde", false, 5716194274910306086]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-http-8a52a013261d584a\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}