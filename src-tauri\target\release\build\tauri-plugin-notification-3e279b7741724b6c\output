cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=C:\WorkSpace\claudia\src-tauri\target\release\build\tauri-plugin-notification-3e279b7741724b6c\out\tauri-plugin-notification-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-notification-2.2.3\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
