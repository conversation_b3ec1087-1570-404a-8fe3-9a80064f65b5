{"rustc": 16531013993302481708, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 170688056642803938, "deps": [[3060637413840920116, "proc_macro2", false, 1615449880013655637], [3150220818285335163, "url", false, 6102992118273930879], [4899080583175475170, "semver", false, 6284967286266319008], [7170110829644101142, "json_patch", false, 279542947189289318], [7392050791754369441, "ico", false, 3465742573613140050], [8319709847752024821, "uuid", false, 17576429577715121075], [9689903380558560274, "serde", false, 17697301536407558215], [9857275760291862238, "sha2", false, 12163567654100877223], [10806645703491011684, "thiserror", false, 12848503974094196907], [11050281405049894993, "tauri_utils", false, 16122735134604026904], [12687914511023397207, "png", false, 7951146328048285048], [13077212702700853852, "base64", false, 7522608780807098969], [14132538657330703225, "brotli", false, 9869235566277786718], [15367738274754116744, "serde_json", false, 10606268451690261834], [15622660310229662834, "walkdir", false, 7957375469078984964], [17990358020177143287, "quote", false, 15017761040731537776], [18149961000318489080, "syn", false, 2644455251149143107]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-ff72de286d185690\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}