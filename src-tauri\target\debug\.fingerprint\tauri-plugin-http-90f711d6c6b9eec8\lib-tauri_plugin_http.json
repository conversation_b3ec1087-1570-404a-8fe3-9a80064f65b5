{"rustc": 16531013993302481708, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 7795770796983219439, "profile": 2241668132362809309, "path": 15281685881539096402, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 8002641282035014302], [3150220818285335163, "url", false, 12307826154636159061], [8218178811151724123, "reqwest", false, 15328367791607731781], [8298091525883606470, "cookie_store", false, 16637569450431072525], [9010263965687315507, "http", false, 5374916934768255852], [9451456094439810778, "regex", false, 7385866613988980781], [9538054652646069845, "tokio", false, 11647163168096858975], [9689903380558560274, "serde", false, 1356923275834828457], [10755362358622467486, "tauri", false, 9659588977301275262], [10806645703491011684, "thiserror", false, 8844752790588992264], [13890802266741835355, "tauri_plugin_fs", false, 556532965761531288], [15367738274754116744, "serde_json", false, 5032607260025433330], [15441187897486245138, "build_script_build", false, 15758603279151548936], [16066129441945555748, "bytes", false, 15597361452344818799], [17047088963840213854, "data_url", false, 5246512318109138945]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-http-90f711d6c6b9eec8\\dep-lib-tauri_plugin_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}