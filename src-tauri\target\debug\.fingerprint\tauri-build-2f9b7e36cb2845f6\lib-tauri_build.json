{"rustc": 16531013993302481708, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 12997780948073429937, "deps": [[4899080583175475170, "semver", false, 6284967286266319008], [6913375703034175521, "schemars", false, 5851682215235040678], [7170110829644101142, "json_patch", false, 279542947189289318], [9689903380558560274, "serde", false, 17697301536407558215], [11050281405049894993, "tauri_utils", false, 16122735134604026904], [12714016054753183456, "tauri_winres", false, 3050222226548083928], [13077543566650298139, "heck", false, 13003241475100688398], [13475171727366188400, "cargo_toml", false, 5332539101432299383], [13625485746686963219, "anyhow", false, 5661029371983558191], [15367738274754116744, "serde_json", false, 10606268451690261834], [15609422047640926750, "toml", false, 8888550214211600567], [15622660310229662834, "walkdir", false, 7957375469078984964], [16928111194414003569, "dirs", false, 18369864429967937553], [17155886227862585100, "glob", false, 16471218984395200217]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-2f9b7e36cb2845f6\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}