{"rustc": 16531013993302481708, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 7859547470675518382, "path": 7088080666952519920, "deps": [[40386456601120721, "percent_encoding", false, 9202197194377805677], [418947936956741439, "h2", false, 5757037659981784763], [778154619793643451, "hyper_util", false, 7443359069071197265], [784494742817713399, "tower_service", false, 13615595949129068892], [1288403060204016458, "tokio_util", false, 6149709018398201610], [1788832197870803419, "hyper_rustls", false, 7684887680606345383], [1906322745568073236, "pin_project_lite", false, 13428441873243081061], [2054153378684941554, "tower_http", false, 16643840635303540394], [2517136641825875337, "sync_wrapper", false, 14723260521964689214], [2883436298747778685, "rustls_pki_types", false, 2539368524067777037], [3150220818285335163, "url", false, 9955794519733322107], [5695049318159433696, "tower", false, 10895932383548945435], [5907992341687085091, "webpki_roots", false, 9943752050360798102], [5986029879202738730, "log", false, 15145306266573603605], [7620660491849607393, "futures_core", false, 11308563171548495787], [8298091525883606470, "cookie_store", false, 7375428158574983772], [9010263965687315507, "http", false, 11117590330939092863], [9538054652646069845, "tokio", false, 10685044774007314925], [9689903380558560274, "serde", false, 7565272720106024856], [10229185211513642314, "mime", false, 7293429583819305088], [10629569228670356391, "futures_util", false, 10080239264288247771], [11895591994124935963, "tokio_rustls", false, 15329112768603493888], [11957360342995674422, "hyper", false, 3978426371719433608], [12186126227181294540, "tokio_native_tls", false, 797668099443869677], [13077212702700853852, "base64", false, 16466660244743038906], [14084095096285906100, "http_body", false, 12285080620219526281], [14564311161534545801, "encoding_rs", false, 9323012540895115995], [15367738274754116744, "serde_json", false, 8303730601567159926], [16066129441945555748, "bytes", false, 16809627282366868376], [16400140949089969347, "rustls", false, 8718200407412011937], [16542808166767769916, "serde_urlencoded", false, 14344462135755441707], [16727543399706004146, "cookie_crate", false, 15309766727643194656], [16785601910559813697, "native_tls_crate", false, 15205447220896442775], [16900715236047033623, "http_body_util", false, 18279944019542257760], [18273243456331255970, "hyper_tls", false, 13573699108918215150]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-46368e2d94eb58e8\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}