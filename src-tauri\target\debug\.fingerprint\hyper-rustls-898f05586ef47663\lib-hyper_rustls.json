{"rustc": 16531013993302481708, "features": "[\"http1\", \"http2\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 15657897354478470176, "path": 18181316325815919262, "deps": [[778154619793643451, "hyper_util", false, 8700261575005116645], [784494742817713399, "tower_service", false, 10329226803908331166], [2883436298747778685, "pki_types", false, 16857439300119542113], [5907992341687085091, "webpki_roots", false, 2234376432967623493], [9010263965687315507, "http", false, 12229602069060356524], [9538054652646069845, "tokio", false, 17887723141469624085], [11895591994124935963, "tokio_rustls", false, 15158511064436920642], [11957360342995674422, "hyper", false, 2167207615499343613], [16400140949089969347, "rustls", false, 849824138720431209]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-rustls-898f05586ef47663\\dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}